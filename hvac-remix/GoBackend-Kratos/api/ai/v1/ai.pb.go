// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/ai/v1/ai.proto

package v1

import (
context "context"
grpc "google.golang.org/grpc"
codes "google.golang.org/grpc/codes"
status "google.golang.org/grpc/status"
)

// ChatRequest represents a chat request
type ChatRequest struct {
Message string   `json:"message,omitempty"`
Model   string   `json:"model,omitempty"`
Context []string `json:"context,omitempty"`
}

func (x *ChatRequest) Reset() { *x = ChatRequest{} }
func (x *ChatRequest) String() string { return "ChatRequest{}" }
func (*ChatRequest) ProtoMessage() {}
func (x *ChatRequest) GetMessage() string { if x != nil { return x.Message }; return "" }
func (x *ChatRequest) GetModel() string { if x != nil { return x.Model }; return "" }
func (x *ChatRequest) GetContext() []string { if x != nil { return x.Context }; return nil }

// ChatResponse represents a chat response
type ChatResponse struct {
Response   string `json:"response,omitempty"`
ModelUsed  string `json:"model_used,omitempty"`
TokensUsed int32  `json:"tokens_used,omitempty"`
}

func (x *ChatResponse) Reset() { *x = ChatResponse{} }
func (x *ChatResponse) String() string { return "ChatResponse{}" }
func (*ChatResponse) ProtoMessage() {}
func (x *ChatResponse) GetResponse() string { if x != nil { return x.Response }; return "" }
func (x *ChatResponse) GetModelUsed() string { if x != nil { return x.ModelUsed }; return "" }
func (x *ChatResponse) GetTokensUsed() int32 { if x != nil { return x.TokensUsed }; return 0 }

// AnalyzeRequest represents an analysis request
type AnalyzeRequest struct {
Content      string `json:"content,omitempty"`
AnalysisType string `json:"analysis_type,omitempty"`
Model        string `json:"model,omitempty"`
}

func (x *AnalyzeRequest) Reset() { *x = AnalyzeRequest{} }
func (x *AnalyzeRequest) String() string { return "AnalyzeRequest{}" }
func (*AnalyzeRequest) ProtoMessage() {}
func (x *AnalyzeRequest) GetContent() string { if x != nil { return x.Content }; return "" }
func (x *AnalyzeRequest) GetAnalysisType() string { if x != nil { return x.AnalysisType }; return "" }
func (x *AnalyzeRequest) GetModel() string { if x != nil { return x.Model }; return "" }

// AnalyzeResponse represents an analysis response
type AnalyzeResponse struct {
Analysis   string            `json:"analysis,omitempty"`
Confidence float32           `json:"confidence,omitempty"`
Metadata   map[string]string `json:"metadata,omitempty"`
}

func (x *AnalyzeResponse) Reset() { *x = AnalyzeResponse{} }
func (x *AnalyzeResponse) String() string { return "AnalyzeResponse{}" }
func (*AnalyzeResponse) ProtoMessage() {}
func (x *AnalyzeResponse) GetAnalysis() string { if x != nil { return x.Analysis }; return "" }
func (x *AnalyzeResponse) GetConfidence() float32 { if x != nil { return x.Confidence }; return 0 }
func (x *AnalyzeResponse) GetMetadata() map[string]string { if x != nil { return x.Metadata }; return nil }

// ListModelsRequest represents a list models request
type ListModelsRequest struct{}

func (x *ListModelsRequest) Reset() { *x = ListModelsRequest{} }
func (x *ListModelsRequest) String() string { return "ListModelsRequest{}" }
func (*ListModelsRequest) ProtoMessage() {}

// ListModelsResponse represents a list models response
type ListModelsResponse struct {
Models []*AIModel `json:"models,omitempty"`
}

func (x *ListModelsResponse) Reset() { *x = ListModelsResponse{} }
func (x *ListModelsResponse) String() string { return "ListModelsResponse{}" }
func (*ListModelsResponse) ProtoMessage() {}
func (x *ListModelsResponse) GetModels() []*AIModel { if x != nil { return x.Models }; return nil }

// AIModel represents an AI model
type AIModel struct {
Name      string `json:"name,omitempty"`
Type      string `json:"type,omitempty"`
Available bool   `json:"available,omitempty"`
Endpoint  string `json:"endpoint,omitempty"`
}

func (x *AIModel) Reset() { *x = AIModel{} }
func (x *AIModel) String() string { return "AIModel{}" }
func (*AIModel) ProtoMessage() {}
func (x *AIModel) GetName() string { if x != nil { return x.Name }; return "" }
func (x *AIModel) GetType() string { if x != nil { return x.Type }; return "" }
func (x *AIModel) GetAvailable() bool { if x != nil { return x.Available }; return false }
func (x *AIModel) GetEndpoint() string { if x != nil { return x.Endpoint }; return "" }

// AIServiceServer is the server API for AIService service.
type AIServiceServer interface {
Chat(context.Context, *ChatRequest) (*ChatResponse, error)
Analyze(context.Context, *AnalyzeRequest) (*AnalyzeResponse, error)
ListModels(context.Context, *ListModelsRequest) (*ListModelsResponse, error)
}

// UnimplementedAIServiceServer can be embedded to have forward compatible implementations.
type UnimplementedAIServiceServer struct{}

func (*UnimplementedAIServiceServer) Chat(context.Context, *ChatRequest) (*ChatResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method Chat not implemented")
}
func (*UnimplementedAIServiceServer) Analyze(context.Context, *AnalyzeRequest) (*AnalyzeResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method Analyze not implemented")
}
func (*UnimplementedAIServiceServer) ListModels(context.Context, *ListModelsRequest) (*ListModelsResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method ListModels not implemented")
}

func RegisterAIServiceServer(s *grpc.Server, srv AIServiceServer) {
s.RegisterService(&_AIService_serviceDesc, srv)
}

var _AIService_serviceDesc = grpc.ServiceDesc{
ServiceName: "api.ai.v1.AIService",
HandlerType: (*AIServiceServer)(nil),
Methods: []grpc.MethodDesc{
{MethodName: "Chat", Handler: _AIService_Chat_Handler},
{MethodName: "Analyze", Handler: _AIService_Analyze_Handler},
{MethodName: "ListModels", Handler: _AIService_ListModels_Handler},
},
Streams:  []grpc.StreamDesc{},
Metadata: "api/ai/v1/ai.proto",
}

func _AIService_Chat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(ChatRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(AIServiceServer).Chat(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.ai.v1.AIService/Chat"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(AIServiceServer).Chat(ctx, req.(*ChatRequest))
}
return interceptor(ctx, in, info, handler)
}

func _AIService_Analyze_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(AnalyzeRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(AIServiceServer).Analyze(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.ai.v1.AIService/Analyze"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(AIServiceServer).Analyze(ctx, req.(*AnalyzeRequest))
}
return interceptor(ctx, in, info, handler)
}

func _AIService_ListModels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(ListModelsRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(AIServiceServer).ListModels(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.ai.v1.AIService/ListModels"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(AIServiceServer).ListModels(ctx, req.(*ListModelsRequest))
}
return interceptor(ctx, in, info, handler)
}
