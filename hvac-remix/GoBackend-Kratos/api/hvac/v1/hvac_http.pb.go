// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: hvac/v1/hvac.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationHVACServiceCreateCustomer = "/api.hvac.v1.HVACService/CreateCustomer"
const OperationHVACServiceGetCustomer = "/api.hvac.v1.HVACService/GetCustomer"
const OperationHVACServiceListCustomers = "/api.hvac.v1.HVACService/ListCustomers"
const OperationHVACServiceCreateJob = "/api.hvac.v1.HVACService/CreateJob"
const OperationHVACServiceGetJob = "/api.hvac.v1.HVACService/GetJob"
const OperationHVACServiceListJobs = "/api.hvac.v1.HVACService/ListJobs"

type HVACServiceHTTPServer interface {
	// CreateCustomer Create a new customer
	CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
	// GetCustomer Get customer by ID
	GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
	// ListCustomers List all customers
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	// CreateJob Create a new job
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	// GetJob Get job by ID
	GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error)
	// ListJobs List all jobs
	ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
}

func RegisterHVACServiceHTTPServer(s *http.Server, srv HVACServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/customers", _HVACService_CreateCustomer0_HTTP_Handler(srv))
	r.GET("/api/v1/customers/{id}", _HVACService_GetCustomer0_HTTP_Handler(srv))
	r.GET("/api/v1/customers", _HVACService_ListCustomers0_HTTP_Handler(srv))
	r.POST("/api/v1/jobs", _HVACService_CreateJob0_HTTP_Handler(srv))
	r.GET("/api/v1/jobs/{id}", _HVACService_GetJob0_HTTP_Handler(srv))
	r.GET("/api/v1/jobs", _HVACService_ListJobs0_HTTP_Handler(srv))
}

func _HVACService_CreateCustomer0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCustomerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceCreateCustomer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCustomer(ctx, req.(*CreateCustomerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCustomerResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetCustomer0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCustomerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetCustomer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCustomer(ctx, req.(*GetCustomerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCustomerResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ListCustomers0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCustomersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceListCustomers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCustomers(ctx, req.(*ListCustomersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCustomersResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_CreateJob0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceCreateJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateJob(ctx, req.(*CreateJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateJobResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_GetJob0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetJobRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceGetJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetJob(ctx, req.(*GetJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetJobResponse)
		return ctx.Result(200, reply)
	}
}

func _HVACService_ListJobs0_HTTP_Handler(srv HVACServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListJobsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHVACServiceListJobs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListJobs(ctx, req.(*ListJobsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListJobsResponse)
		return ctx.Result(200, reply)
	}
}

type HVACServiceHTTPClient interface {
	CreateCustomer(ctx context.Context, req *CreateCustomerRequest, opts ...http.CallOption) (rsp *CreateCustomerResponse, err error)
	GetCustomer(ctx context.Context, req *GetCustomerRequest, opts ...http.CallOption) (rsp *GetCustomerResponse, err error)
	ListCustomers(ctx context.Context, req *ListCustomersRequest, opts ...http.CallOption) (rsp *ListCustomersResponse, err error)
	CreateJob(ctx context.Context, req *CreateJobRequest, opts ...http.CallOption) (rsp *CreateJobResponse, err error)
	GetJob(ctx context.Context, req *GetJobRequest, opts ...http.CallOption) (rsp *GetJobResponse, err error)
	ListJobs(ctx context.Context, req *ListJobsRequest, opts ...http.CallOption) (rsp *ListJobsResponse, err error)
}

type HVACServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewHVACServiceHTTPClient(client *http.Client) HVACServiceHTTPClient {
	return &HVACServiceHTTPClientImpl{client}
}

func (c *HVACServiceHTTPClientImpl) CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...http.CallOption) (*CreateCustomerResponse, error) {
	var out CreateCustomerResponse
	pattern := "/api/v1/customers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceCreateCustomer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...http.CallOption) (*GetCustomerResponse, error) {
	var out GetCustomerResponse
	pattern := "/api/v1/customers/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetCustomer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...http.CallOption) (*ListCustomersResponse, error) {
	var out ListCustomersResponse
	pattern := "/api/v1/customers"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceListCustomers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...http.CallOption) (*CreateJobResponse, error) {
	var out CreateJobResponse
	pattern := "/api/v1/jobs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationHVACServiceCreateJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) GetJob(ctx context.Context, in *GetJobRequest, opts ...http.CallOption) (*GetJobResponse, error) {
	var out GetJobResponse
	pattern := "/api/v1/jobs/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceGetJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HVACServiceHTTPClientImpl) ListJobs(ctx context.Context, in *ListJobsRequest, opts ...http.CallOption) (*ListJobsResponse, error) {
	var out ListJobsResponse
	pattern := "/api/v1/jobs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHVACServiceListJobs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
