syntax = "proto3";

package api.hvac.v1;

option go_package = "gobackend-hvac-kratos/api/hvac/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// HVAC Service Definition
service HVACService {
  // Customer Management
  rpc CreateCustomer(CreateCustomerRequest) returns (CreateCustomerResponse) {
    option (google.api.http) = {
      post: "/api/v1/customers"
      body: "*"
    };
  }

  rpc GetCustomer(GetCustomerRequest) returns (GetCustomerResponse) {
    option (google.api.http) = {
      get: "/api/v1/customers/{id}"
    };
  }

  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse) {
    option (google.api.http) = {
      get: "/api/v1/customers"
    };
  }

  // Job Management
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse) {
    option (google.api.http) = {
      post: "/api/v1/jobs"
      body: "*"
    };
  }

  rpc GetJob(GetJobRequest) returns (GetJobResponse) {
    option (google.api.http) = {
      get: "/api/v1/jobs/{id}"
    };
  }

  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/api/v1/jobs"
    };
  }
}

// Message Definitions
message Customer {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string address = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message Job {
  int64 id = 1;
  int64 customer_id = 2;
  string title = 3;
  string description = 4;
  string status = 5;
  string priority = 6;
  google.protobuf.Timestamp scheduled_at = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}

// Request/Response Messages
message CreateCustomerRequest {
  string name = 1;
  string email = 2;
  string phone = 3;
  string address = 4;
}

message CreateCustomerResponse {
  Customer customer = 1;
}

message GetCustomerRequest {
  int64 id = 1;
}

message GetCustomerResponse {
  Customer customer = 1;
}

message ListCustomersRequest {
  int32 page = 1;
  int32 page_size = 2;
}

message ListCustomersResponse {
  repeated Customer customers = 1;
  int32 total = 2;
}

message CreateJobRequest {
  int64 customer_id = 1;
  string title = 2;
  string description = 3;
  string priority = 4;
  google.protobuf.Timestamp scheduled_at = 5;
}

message CreateJobResponse {
  Job job = 1;
}

message GetJobRequest {
  int64 id = 1;
}

message GetJobResponse {
  Job job = 1;
}

message ListJobsRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 customer_id = 3;
  string status = 4;
}

message ListJobsResponse {
  repeated Job jobs = 1;
  int32 total = 2;
}