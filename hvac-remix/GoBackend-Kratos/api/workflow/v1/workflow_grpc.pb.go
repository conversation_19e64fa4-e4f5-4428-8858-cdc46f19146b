// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: workflow/v1/workflow.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WorkflowService_CreateWorkflowRule_FullMethodName         = "/api.workflow.v1.WorkflowService/CreateWorkflowRule"
	WorkflowService_GetWorkflowRules_FullMethodName           = "/api.workflow.v1.WorkflowService/GetWorkflowRules"
	WorkflowService_ExecuteWorkflowsForTrigger_FullMethodName = "/api.workflow.v1.WorkflowService/ExecuteWorkflowsForTrigger"
	WorkflowService_GetWorkflowExecutions_FullMethodName      = "/api.workflow.v1.WorkflowService/GetWorkflowExecutions"
	WorkflowService_GetWorkflowTemplates_FullMethodName       = "/api.workflow.v1.WorkflowService/GetWorkflowTemplates"
	WorkflowService_CreateWorkflowFromTemplate_FullMethodName = "/api.workflow.v1.WorkflowService/CreateWorkflowFromTemplate"
	WorkflowService_UpdateWorkflowRule_FullMethodName         = "/api.workflow.v1.WorkflowService/UpdateWorkflowRule"
	WorkflowService_DeleteWorkflowRule_FullMethodName         = "/api.workflow.v1.WorkflowService/DeleteWorkflowRule"
	WorkflowService_HealthCheck_FullMethodName                = "/api.workflow.v1.WorkflowService/HealthCheck"
)

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ⚡ Workflow Service - Advanced Process Automation API
type WorkflowServiceClient interface {
	// Create workflow rule
	CreateWorkflowRule(ctx context.Context, in *CreateWorkflowRuleRequest, opts ...grpc.CallOption) (*CreateWorkflowRuleResponse, error)
	// Get workflow rules
	GetWorkflowRules(ctx context.Context, in *GetWorkflowRulesRequest, opts ...grpc.CallOption) (*GetWorkflowRulesResponse, error)
	// Execute workflows for trigger
	ExecuteWorkflowsForTrigger(ctx context.Context, in *ExecuteWorkflowsForTriggerRequest, opts ...grpc.CallOption) (*ExecuteWorkflowsForTriggerResponse, error)
	// Get workflow executions
	GetWorkflowExecutions(ctx context.Context, in *GetWorkflowExecutionsRequest, opts ...grpc.CallOption) (*GetWorkflowExecutionsResponse, error)
	// Get workflow templates
	GetWorkflowTemplates(ctx context.Context, in *GetWorkflowTemplatesRequest, opts ...grpc.CallOption) (*GetWorkflowTemplatesResponse, error)
	// Create workflow from template
	CreateWorkflowFromTemplate(ctx context.Context, in *CreateWorkflowFromTemplateRequest, opts ...grpc.CallOption) (*CreateWorkflowFromTemplateResponse, error)
	// Update workflow rule
	UpdateWorkflowRule(ctx context.Context, in *UpdateWorkflowRuleRequest, opts ...grpc.CallOption) (*UpdateWorkflowRuleResponse, error)
	// Delete workflow rule
	DeleteWorkflowRule(ctx context.Context, in *DeleteWorkflowRuleRequest, opts ...grpc.CallOption) (*DeleteWorkflowRuleResponse, error)
	// Health check
	HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) CreateWorkflowRule(ctx context.Context, in *CreateWorkflowRuleRequest, opts ...grpc.CallOption) (*CreateWorkflowRuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, WorkflowService_CreateWorkflowRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowRules(ctx context.Context, in *GetWorkflowRulesRequest, opts ...grpc.CallOption) (*GetWorkflowRulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWorkflowRulesResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetWorkflowRules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ExecuteWorkflowsForTrigger(ctx context.Context, in *ExecuteWorkflowsForTriggerRequest, opts ...grpc.CallOption) (*ExecuteWorkflowsForTriggerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExecuteWorkflowsForTriggerResponse)
	err := c.cc.Invoke(ctx, WorkflowService_ExecuteWorkflowsForTrigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowExecutions(ctx context.Context, in *GetWorkflowExecutionsRequest, opts ...grpc.CallOption) (*GetWorkflowExecutionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWorkflowExecutionsResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetWorkflowExecutions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowTemplates(ctx context.Context, in *GetWorkflowTemplatesRequest, opts ...grpc.CallOption) (*GetWorkflowTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWorkflowTemplatesResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetWorkflowTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) CreateWorkflowFromTemplate(ctx context.Context, in *CreateWorkflowFromTemplateRequest, opts ...grpc.CallOption) (*CreateWorkflowFromTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateWorkflowFromTemplateResponse)
	err := c.cc.Invoke(ctx, WorkflowService_CreateWorkflowFromTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowRule(ctx context.Context, in *UpdateWorkflowRuleRequest, opts ...grpc.CallOption) (*UpdateWorkflowRuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, WorkflowService_UpdateWorkflowRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) DeleteWorkflowRule(ctx context.Context, in *DeleteWorkflowRuleRequest, opts ...grpc.CallOption) (*DeleteWorkflowRuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, WorkflowService_DeleteWorkflowRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, WorkflowService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility.
//
// ⚡ Workflow Service - Advanced Process Automation API
type WorkflowServiceServer interface {
	// Create workflow rule
	CreateWorkflowRule(context.Context, *CreateWorkflowRuleRequest) (*CreateWorkflowRuleResponse, error)
	// Get workflow rules
	GetWorkflowRules(context.Context, *GetWorkflowRulesRequest) (*GetWorkflowRulesResponse, error)
	// Execute workflows for trigger
	ExecuteWorkflowsForTrigger(context.Context, *ExecuteWorkflowsForTriggerRequest) (*ExecuteWorkflowsForTriggerResponse, error)
	// Get workflow executions
	GetWorkflowExecutions(context.Context, *GetWorkflowExecutionsRequest) (*GetWorkflowExecutionsResponse, error)
	// Get workflow templates
	GetWorkflowTemplates(context.Context, *GetWorkflowTemplatesRequest) (*GetWorkflowTemplatesResponse, error)
	// Create workflow from template
	CreateWorkflowFromTemplate(context.Context, *CreateWorkflowFromTemplateRequest) (*CreateWorkflowFromTemplateResponse, error)
	// Update workflow rule
	UpdateWorkflowRule(context.Context, *UpdateWorkflowRuleRequest) (*UpdateWorkflowRuleResponse, error)
	// Delete workflow rule
	DeleteWorkflowRule(context.Context, *DeleteWorkflowRuleRequest) (*DeleteWorkflowRuleResponse, error)
	// Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWorkflowServiceServer struct{}

func (UnimplementedWorkflowServiceServer) CreateWorkflowRule(context.Context, *CreateWorkflowRuleRequest) (*CreateWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowRules(context.Context, *GetWorkflowRulesRequest) (*GetWorkflowRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowRules not implemented")
}
func (UnimplementedWorkflowServiceServer) ExecuteWorkflowsForTrigger(context.Context, *ExecuteWorkflowsForTriggerRequest) (*ExecuteWorkflowsForTriggerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteWorkflowsForTrigger not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowExecutions(context.Context, *GetWorkflowExecutionsRequest) (*GetWorkflowExecutionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowExecutions not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowTemplates(context.Context, *GetWorkflowTemplatesRequest) (*GetWorkflowTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowTemplates not implemented")
}
func (UnimplementedWorkflowServiceServer) CreateWorkflowFromTemplate(context.Context, *CreateWorkflowFromTemplateRequest) (*CreateWorkflowFromTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflowFromTemplate not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowRule(context.Context, *UpdateWorkflowRuleRequest) (*UpdateWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) DeleteWorkflowRule(context.Context, *DeleteWorkflowRuleRequest) (*DeleteWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}
func (UnimplementedWorkflowServiceServer) testEmbeddedByValue()                         {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	// If the following call pancis, it indicates UnimplementedWorkflowServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_CreateWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_CreateWorkflowRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflowRule(ctx, req.(*CreateWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetWorkflowRules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowRules(ctx, req.(*GetWorkflowRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ExecuteWorkflowsForTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteWorkflowsForTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ExecuteWorkflowsForTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_ExecuteWorkflowsForTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ExecuteWorkflowsForTrigger(ctx, req.(*ExecuteWorkflowsForTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowExecutions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowExecutionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowExecutions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetWorkflowExecutions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowExecutions(ctx, req.(*GetWorkflowExecutionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetWorkflowTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowTemplates(ctx, req.(*GetWorkflowTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_CreateWorkflowFromTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowFromTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflowFromTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_CreateWorkflowFromTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflowFromTemplate(ctx, req.(*CreateWorkflowFromTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_UpdateWorkflowRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowRule(ctx, req.(*UpdateWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_DeleteWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).DeleteWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_DeleteWorkflowRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).DeleteWorkflowRule(ctx, req.(*DeleteWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).HealthCheck(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.workflow.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWorkflowRule",
			Handler:    _WorkflowService_CreateWorkflowRule_Handler,
		},
		{
			MethodName: "GetWorkflowRules",
			Handler:    _WorkflowService_GetWorkflowRules_Handler,
		},
		{
			MethodName: "ExecuteWorkflowsForTrigger",
			Handler:    _WorkflowService_ExecuteWorkflowsForTrigger_Handler,
		},
		{
			MethodName: "GetWorkflowExecutions",
			Handler:    _WorkflowService_GetWorkflowExecutions_Handler,
		},
		{
			MethodName: "GetWorkflowTemplates",
			Handler:    _WorkflowService_GetWorkflowTemplates_Handler,
		},
		{
			MethodName: "CreateWorkflowFromTemplate",
			Handler:    _WorkflowService_CreateWorkflowFromTemplate_Handler,
		},
		{
			MethodName: "UpdateWorkflowRule",
			Handler:    _WorkflowService_UpdateWorkflowRule_Handler,
		},
		{
			MethodName: "DeleteWorkflowRule",
			Handler:    _WorkflowService_DeleteWorkflowRule_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _WorkflowService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "workflow/v1/workflow.proto",
}
