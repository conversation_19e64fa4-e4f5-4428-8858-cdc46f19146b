// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: api/workflow/v1/workflow.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkflowServiceClient interface {
	// Create workflow rule
	CreateWorkflowRule(ctx context.Context, in *CreateWorkflowRuleRequest, opts ...grpc.CallOption) (*CreateWorkflowRuleResponse, error)
	// Get workflow rules
	GetWorkflowRules(ctx context.Context, in *GetWorkflowRulesRequest, opts ...grpc.CallOption) (*GetWorkflowRulesResponse, error)
	// Execute workflows for trigger
	ExecuteWorkflowsForTrigger(ctx context.Context, in *ExecuteWorkflowsForTriggerRequest, opts ...grpc.CallOption) (*ExecuteWorkflowsForTriggerResponse, error)
	// Get workflow executions
	GetWorkflowExecutions(ctx context.Context, in *GetWorkflowExecutionsRequest, opts ...grpc.CallOption) (*GetWorkflowExecutionsResponse, error)
	// Get workflow templates
	GetWorkflowTemplates(ctx context.Context, in *GetWorkflowTemplatesRequest, opts ...grpc.CallOption) (*GetWorkflowTemplatesResponse, error)
	// Create workflow from template
	CreateWorkflowFromTemplate(ctx context.Context, in *CreateWorkflowFromTemplateRequest, opts ...grpc.CallOption) (*CreateWorkflowFromTemplateResponse, error)
	// Update workflow rule
	UpdateWorkflowRule(ctx context.Context, in *UpdateWorkflowRuleRequest, opts ...grpc.CallOption) (*UpdateWorkflowRuleResponse, error)
	// Delete workflow rule
	DeleteWorkflowRule(ctx context.Context, in *DeleteWorkflowRuleRequest, opts ...grpc.CallOption) (*DeleteWorkflowRuleResponse, error)
	// Health check
	HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) CreateWorkflowRule(ctx context.Context, in *CreateWorkflowRuleRequest, opts ...grpc.CallOption) (*CreateWorkflowRuleResponse, error) {
	out := new(CreateWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/CreateWorkflowRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowRules(ctx context.Context, in *GetWorkflowRulesRequest, opts ...grpc.CallOption) (*GetWorkflowRulesResponse, error) {
	out := new(GetWorkflowRulesResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/GetWorkflowRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ExecuteWorkflowsForTrigger(ctx context.Context, in *ExecuteWorkflowsForTriggerRequest, opts ...grpc.CallOption) (*ExecuteWorkflowsForTriggerResponse, error) {
	out := new(ExecuteWorkflowsForTriggerResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/ExecuteWorkflowsForTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowExecutions(ctx context.Context, in *GetWorkflowExecutionsRequest, opts ...grpc.CallOption) (*GetWorkflowExecutionsResponse, error) {
	out := new(GetWorkflowExecutionsResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/GetWorkflowExecutions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowTemplates(ctx context.Context, in *GetWorkflowTemplatesRequest, opts ...grpc.CallOption) (*GetWorkflowTemplatesResponse, error) {
	out := new(GetWorkflowTemplatesResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/GetWorkflowTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) CreateWorkflowFromTemplate(ctx context.Context, in *CreateWorkflowFromTemplateRequest, opts ...grpc.CallOption) (*CreateWorkflowFromTemplateResponse, error) {
	out := new(CreateWorkflowFromTemplateResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/CreateWorkflowFromTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowRule(ctx context.Context, in *UpdateWorkflowRuleRequest, opts ...grpc.CallOption) (*UpdateWorkflowRuleResponse, error) {
	out := new(UpdateWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/UpdateWorkflowRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) DeleteWorkflowRule(ctx context.Context, in *DeleteWorkflowRuleRequest, opts ...grpc.CallOption) (*DeleteWorkflowRuleResponse, error) {
	out := new(DeleteWorkflowRuleResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/DeleteWorkflowRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, "/api.workflow.v1.WorkflowService/HealthCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations should embed UnimplementedWorkflowServiceServer
// for forward compatibility
type WorkflowServiceServer interface {
	// Create workflow rule
	CreateWorkflowRule(context.Context, *CreateWorkflowRuleRequest) (*CreateWorkflowRuleResponse, error)
	// Get workflow rules
	GetWorkflowRules(context.Context, *GetWorkflowRulesRequest) (*GetWorkflowRulesResponse, error)
	// Execute workflows for trigger
	ExecuteWorkflowsForTrigger(context.Context, *ExecuteWorkflowsForTriggerRequest) (*ExecuteWorkflowsForTriggerResponse, error)
	// Get workflow executions
	GetWorkflowExecutions(context.Context, *GetWorkflowExecutionsRequest) (*GetWorkflowExecutionsResponse, error)
	// Get workflow templates
	GetWorkflowTemplates(context.Context, *GetWorkflowTemplatesRequest) (*GetWorkflowTemplatesResponse, error)
	// Create workflow from template
	CreateWorkflowFromTemplate(context.Context, *CreateWorkflowFromTemplateRequest) (*CreateWorkflowFromTemplateResponse, error)
	// Update workflow rule
	UpdateWorkflowRule(context.Context, *UpdateWorkflowRuleRequest) (*UpdateWorkflowRuleResponse, error)
	// Delete workflow rule
	DeleteWorkflowRule(context.Context, *DeleteWorkflowRuleRequest) (*DeleteWorkflowRuleResponse, error)
	// Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
}

// UnimplementedWorkflowServiceServer should be embedded to have forward compatible implementations.
type UnimplementedWorkflowServiceServer struct {
}

func (UnimplementedWorkflowServiceServer) CreateWorkflowRule(context.Context, *CreateWorkflowRuleRequest) (*CreateWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowRules(context.Context, *GetWorkflowRulesRequest) (*GetWorkflowRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowRules not implemented")
}
func (UnimplementedWorkflowServiceServer) ExecuteWorkflowsForTrigger(context.Context, *ExecuteWorkflowsForTriggerRequest) (*ExecuteWorkflowsForTriggerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteWorkflowsForTrigger not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowExecutions(context.Context, *GetWorkflowExecutionsRequest) (*GetWorkflowExecutionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowExecutions not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowTemplates(context.Context, *GetWorkflowTemplatesRequest) (*GetWorkflowTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowTemplates not implemented")
}
func (UnimplementedWorkflowServiceServer) CreateWorkflowFromTemplate(context.Context, *CreateWorkflowFromTemplateRequest) (*CreateWorkflowFromTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflowFromTemplate not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowRule(context.Context, *UpdateWorkflowRuleRequest) (*UpdateWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) DeleteWorkflowRule(context.Context, *DeleteWorkflowRuleRequest) (*DeleteWorkflowRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkflowRule not implemented")
}
func (UnimplementedWorkflowServiceServer) HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_CreateWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/CreateWorkflowRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflowRule(ctx, req.(*CreateWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/GetWorkflowRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowRules(ctx, req.(*GetWorkflowRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ExecuteWorkflowsForTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteWorkflowsForTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ExecuteWorkflowsForTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/ExecuteWorkflowsForTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ExecuteWorkflowsForTrigger(ctx, req.(*ExecuteWorkflowsForTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowExecutions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowExecutionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowExecutions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/GetWorkflowExecutions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowExecutions(ctx, req.(*GetWorkflowExecutionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/GetWorkflowTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowTemplates(ctx, req.(*GetWorkflowTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_CreateWorkflowFromTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowFromTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflowFromTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/CreateWorkflowFromTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflowFromTemplate(ctx, req.(*CreateWorkflowFromTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/UpdateWorkflowRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowRule(ctx, req.(*UpdateWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_DeleteWorkflowRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkflowRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).DeleteWorkflowRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/DeleteWorkflowRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).DeleteWorkflowRule(ctx, req.(*DeleteWorkflowRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.workflow.v1.WorkflowService/HealthCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).HealthCheck(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.workflow.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateWorkflowRule",
			Handler:    _WorkflowService_CreateWorkflowRule_Handler,
		},
		{
			MethodName: "GetWorkflowRules",
			Handler:    _WorkflowService_GetWorkflowRules_Handler,
		},
		{
			MethodName: "ExecuteWorkflowsForTrigger",
			Handler:    _WorkflowService_ExecuteWorkflowsForTrigger_Handler,
		},
		{
			MethodName: "GetWorkflowExecutions",
			Handler:    _WorkflowService_GetWorkflowExecutions_Handler,
		},
		{
			MethodName: "GetWorkflowTemplates",
			Handler:    _WorkflowService_GetWorkflowTemplates_Handler,
		},
		{
			MethodName: "CreateWorkflowFromTemplate",
			Handler:    _WorkflowService_CreateWorkflowFromTemplate_Handler,
		},
		{
			MethodName: "UpdateWorkflowRule",
			Handler:    _WorkflowService_UpdateWorkflowRule_Handler,
		},
		{
			MethodName: "DeleteWorkflowRule",
			Handler:    _WorkflowService_DeleteWorkflowRule_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _WorkflowService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/workflow/v1/workflow.proto",
}
