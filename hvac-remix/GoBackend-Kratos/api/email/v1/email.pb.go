// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.12.4
// source: api/email/v1/email.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// EmailMessage represents an email message
type EmailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	From      string                 `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To        []string               `protobuf:"bytes,3,rep,name=to,proto3" json:"to,omitempty"`
	Cc        []string               `protobuf:"bytes,4,rep,name=cc,proto3" json:"cc,omitempty"`
	Bcc       []string               `protobuf:"bytes,5,rep,name=bcc,proto3" json:"bcc,omitempty"`
	Subject   string                 `protobuf:"bytes,6,opt,name=subject,proto3" json:"subject,omitempty"`
	Body      string                 `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`
	HtmlBody  string                 `protobuf:"bytes,8,opt,name=html_body,json=htmlBody,proto3" json:"html_body,omitempty"`
	Priority  string                 `protobuf:"bytes,11,opt,name=priority,proto3" json:"priority,omitempty"`
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Status    string                 `protobuf:"bytes,13,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *EmailMessage) Reset() {
	*x = EmailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_email_v1_email_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailMessage) ProtoMessage() {}

func (x *EmailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_api_email_v1_email_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailMessage.ProtoReflect.Descriptor instead.
func (*EmailMessage) Descriptor() ([]byte, []int) {
	return file_api_email_v1_email_proto_rawDescGZIP(), []int{0}
}

func (x *EmailMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmailMessage) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *EmailMessage) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *EmailMessage) GetCc() []string {
	if x != nil {
		return x.Cc
	}
	return nil
}

func (x *EmailMessage) GetBcc() []string {
	if x != nil {
		return x.Bcc
	}
	return nil
}

func (x *EmailMessage) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *EmailMessage) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *EmailMessage) GetHtmlBody() string {
	if x != nil {
		return x.HtmlBody
	}
	return ""
}

func (x *EmailMessage) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *EmailMessage) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *EmailMessage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// SendEmailRequest represents a request to send an email
type SendEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From     string   `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To       []string `protobuf:"bytes,2,rep,name=to,proto3" json:"to,omitempty"`
	Cc       []string `protobuf:"bytes,3,rep,name=cc,proto3" json:"cc,omitempty"`
	Bcc      []string `protobuf:"bytes,4,rep,name=bcc,proto3" json:"bcc,omitempty"`
	Subject  string   `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	Body     string   `protobuf:"bytes,6,opt,name=body,proto3" json:"body,omitempty"`
	HtmlBody string   `protobuf:"bytes,7,opt,name=html_body,json=htmlBody,proto3" json:"html_body,omitempty"`
	Priority string   `protobuf:"bytes,9,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *SendEmailRequest) Reset() {
	*x = SendEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_email_v1_email_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailRequest) ProtoMessage() {}

func (x *SendEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_email_v1_email_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailRequest.ProtoReflect.Descriptor instead.
func (*SendEmailRequest) Descriptor() ([]byte, []int) {
	return file_api_email_v1_email_proto_rawDescGZIP(), []int{1}
}

func (x *SendEmailRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SendEmailRequest) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SendEmailRequest) GetCc() []string {
	if x != nil {
		return x.Cc
	}
	return nil
}

func (x *SendEmailRequest) GetBcc() []string {
	if x != nil {
		return x.Bcc
	}
	return nil
}

func (x *SendEmailRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *SendEmailRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *SendEmailRequest) GetHtmlBody() string {
	if x != nil {
		return x.HtmlBody
	}
	return ""
}

func (x *SendEmailRequest) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

// SendEmailResponse represents a response to send email
type SendEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmailId string `protobuf:"bytes,1,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	Status  string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SendEmailResponse) Reset() {
	*x = SendEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_email_v1_email_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailResponse) ProtoMessage() {}

func (x *SendEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_email_v1_email_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailResponse.ProtoReflect.Descriptor instead.
func (*SendEmailResponse) Descriptor() ([]byte, []int) {
	return file_api_email_v1_email_proto_rawDescGZIP(), []int{2}
}

func (x *SendEmailResponse) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *SendEmailResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SendEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var file_api_email_v1_email_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2e, 0x76, 0x31, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x6f, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2d, 0x68, 0x76, 0x61, 0x63, 0x2d, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_email_v1_email_proto_msgTypes = make([]protoimpl.MessageInfo, 3)

func file_api_email_v1_email_proto_rawDescGZIP() []byte {
	return file_api_email_v1_email_proto_rawDesc
}

// EmailServiceClient is the client API for EmailService service.
type EmailServiceClient interface {
	SendEmail(ctx context.Context, in *SendEmailRequest, opts ...grpc.CallOption) (*SendEmailResponse, error)
}

type emailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailServiceClient(cc grpc.ClientConnInterface) EmailServiceClient {
	return &emailServiceClient{cc}
}

func (c *emailServiceClient) SendEmail(ctx context.Context, in *SendEmailRequest, opts ...grpc.CallOption) (*SendEmailResponse, error) {
	out := new(SendEmailResponse)
	err := c.cc.Invoke(ctx, "/api.email.v1.EmailService/SendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailServiceServer is the server API for EmailService service.
type EmailServiceServer interface {
	SendEmail(context.Context, *SendEmailRequest) (*SendEmailResponse, error)
}

// UnimplementedEmailServiceServer can be embedded to have forward compatible implementations.
type UnimplementedEmailServiceServer struct {
}

func (*UnimplementedEmailServiceServer) SendEmail(context.Context, *SendEmailRequest) (*SendEmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}

func RegisterEmailServiceServer(s *grpc.Server, srv EmailServiceServer) {
	s.RegisterService(&_EmailService_serviceDesc, srv)
}

var _EmailService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "api.email.v1.EmailService",
	HandlerType: (*EmailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendEmail",
			Handler:    _EmailService_SendEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/email/v1/email.proto",
}

func _EmailService_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServiceServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.email.v1.EmailService/SendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServiceServer).SendEmail(ctx, req.(*SendEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}
