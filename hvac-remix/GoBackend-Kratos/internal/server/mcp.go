package server

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/metoro-io/mcp-golang"
	"github.com/metoro-io/mcp-golang/transport/stdio"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	// "gobackend-hvac-kratos/internal/service" // Temporarily unused
	// pb "gobackend-hvac-kratos/api/email/v1" // Temporarily unused
)

// MCPServer wraps the MCP server functionality
type MCPServer struct {
	server      *mcp_golang.Server
	customerUc  *biz.CustomerUsecase
	jobUc       *biz.JobUsecase
	aiUc        *biz.AIUsecase
	// emailSvc    *service.EmailService // Temporarily removed
	log         *log.Helper
}

// HVAC Tool Arguments
type CreateCustomerArgs struct {
	Name    string `json:"name" jsonschema:"required,description=Customer name"`
	Email   string `json:"email" jsonschema:"required,description=Customer email address"`
	Phone   string `json:"phone" jsonschema:"description=Customer phone number"`
	Address string `json:"address" jsonschema:"description=Customer address"`
}

type CreateJobArgs struct {
	CustomerID  int64  `json:"customer_id" jsonschema:"required,description=Customer ID"`
	Title       string `json:"title" jsonschema:"required,description=Job title"`
	Description string `json:"description" jsonschema:"description=Job description"`
	Priority    string `json:"priority" jsonschema:"description=Job priority (low, medium, high, urgent)"`
}

type AIAnalyzeArgs struct {
	Content      string `json:"content" jsonschema:"required,description=Content to analyze"`
	AnalysisType string `json:"analysis_type" jsonschema:"description=Type of analysis to perform"`
	Model        string `json:"model" jsonschema:"description=AI model to use"`
}

type SendEmailArgs struct {
	From     string   `json:"from" jsonschema:"required,description=Sender email address"`
	To       []string `json:"to" jsonschema:"required,description=Recipient email addresses"`
	Subject  string   `json:"subject" jsonschema:"required,description=Email subject"`
	Body     string   `json:"body" jsonschema:"required,description=Email body content"`
	HTMLBody string   `json:"html_body" jsonschema:"description=HTML email body"`
	Priority string   `json:"priority" jsonschema:"description=Email priority (low, normal, high)"`
}

type HVACAdviceArgs struct {
	CustomerIssue string `json:"customer_issue" jsonschema:"required,description=Customer's HVAC issue description"`
	SystemType    string `json:"system_type" jsonschema:"description=Type of HVAC system (central air, heat pump, etc.)"`
}

// NewMCPServer creates a new MCP server
func NewMCPServer(
	c *conf.MCP,
	customerUc *biz.CustomerUsecase,
	jobUc *biz.JobUsecase,
	aiUc *biz.AIUsecase,
	// emailSvc *service.EmailService, // Temporarily removed
	logger log.Logger,
) *MCPServer {
	transport := stdio.NewStdioServerTransport()
	server := mcp_golang.NewServer(transport)

	mcpServer := &MCPServer{
		server:     server,
		customerUc: customerUc,
		jobUc:      jobUc,
		aiUc:       aiUc,
		// emailSvc:   emailSvc, // Temporarily removed
		log:        log.NewHelper(logger),
	}

	// Register tools if enabled (simplified for now)
	// TODO: Implement proper tools configuration
	mcpServer.registerTools(nil)

	return mcpServer
}// registerTools registers MCP tools
func (s *MCPServer) registerTools(toolsConfig interface{}) {
	// Simplified registration for now
	s.registerHVACTools()
	// s.registerEmailTools() // Temporarily disabled
}

// registerHVACTools registers HVAC-specific tools
func (s *MCPServer) registerHVACTools() {
	// Create Customer Tool
	s.server.RegisterTool("create_customer", "Create a new HVAC customer",
		func(args CreateCustomerArgs) (*mcp_golang.ToolResponse, error) {
			customer := &biz.Customer{
				Name:    args.Name,
				Email:   args.Email,
				Phone:   args.Phone,
				Address: args.Address,
			}

			result, err := s.customerUc.CreateCustomer(context.Background(), customer)
			if err != nil {
				return nil, err
			}

			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Customer created successfully: ID=%d, Name=%s", result.ID, result.Name)),
			), nil
		})

	// Create Job Tool
	s.server.RegisterTool("create_job", "Create a new HVAC job",
		func(args CreateJobArgs) (*mcp_golang.ToolResponse, error) {
			job := &biz.Job{
				CustomerID:  args.CustomerID,
				Title:       args.Title,
				Description: args.Description,
				Priority:    args.Priority,
			}

			result, err := s.jobUc.CreateJob(context.Background(), job)
			if err != nil {
				return nil, err
			}

			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Job created successfully: ID=%d, Title=%s", result.ID, result.Title)),
			), nil
		})
}

// registerEmailTools registers email-related tools
func (s *MCPServer) registerEmailTools() {
	// AI Analyze Tool
	s.server.RegisterTool("ai_analyze", "Analyze content using AI",
		func(args AIAnalyzeArgs) (*mcp_golang.ToolResponse, error) {
			req := &biz.AnalyzeRequest{
				Content:      args.Content,
				AnalysisType: args.AnalysisType,
				Model:        args.Model,
			}

			result, err := s.aiUc.Analyze(context.Background(), req)
			if err != nil {
				return nil, err
			}

			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Analysis: %s (Confidence: %.2f)", result.Analysis, result.Confidence)),
			), nil
		})

	// Send Email Tool (temporarily disabled)
	// TODO: Re-enable when EmailService is available

	// HVAC Advice Tool using Gemma
	s.server.RegisterTool("hvac_advice", "Get professional HVAC advice using AI",
		func(args HVACAdviceArgs) (*mcp_golang.ToolResponse, error) {
			// This would use the Gemma GGUF service directly
			// For now, use the AI usecase
			req := &biz.ChatRequest{
				Message: fmt.Sprintf("Customer Issue: %s\nSystem Type: %s\n\nProvide professional HVAC advice.", args.CustomerIssue, args.SystemType),
				Model:   "gemma-3-4b-it-qat-q4_0-gguf",
			}

			result, err := s.aiUc.Chat(context.Background(), req)
			if err != nil {
				return nil, err
			}

			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("HVAC Advice: %s", result.Response)),
			), nil
		})
}// Serve starts the MCP server
func (s *MCPServer) Serve() error {
	s.log.Info("Starting MCP server...")
	return s.server.Serve()
}

// Stop stops the MCP server
func (s *MCPServer) Stop() error {
	s.log.Info("Stopping MCP server...")
	// MCP server doesn't have explicit stop method in current implementation
	return nil
}