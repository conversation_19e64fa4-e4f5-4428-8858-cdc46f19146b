package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// ⚡ Workflow Business Logic Layer
// GoBackend-Kratos HVAC CRM System

// WorkflowRepo defines the workflow repository interface
type WorkflowRepo interface {
	// Rule management
	CreateWorkflowRule(ctx context.Context, rule *WorkflowRule) error
	GetWorkflowRules(ctx context.Context, triggerType string, isActive *bool) ([]*WorkflowRule, error)
	UpdateWorkflowRule(ctx context.Context, rule *WorkflowRule) error
	DeleteWorkflowRule(ctx context.Context, ruleID uint) error
	
	// Execution management
	CreateWorkflowExecution(ctx context.Context, execution *WorkflowExecution) error
	GetWorkflowExecutions(ctx context.Context, ruleID *uint, status string, startDate, endDate time.Time, page, pageSize int) ([]*WorkflowExecution, int, error)
	UpdateWorkflowExecution(ctx context.Context, execution *WorkflowExecution) error
	
	// Template management
	GetWorkflowTemplates(ctx context.Context, category string) ([]*WorkflowTemplate, error)
	CreateWorkflowFromTemplate(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*WorkflowRule, error)
	
	// Statistics
	UpdateRuleStatistics(ctx context.Context, ruleID uint, success bool) error
}

// WorkflowUsecase handles workflow business logic
type WorkflowUsecase struct {
	repo WorkflowRepo
	log  *log.Helper
}

// NewWorkflowUsecase creates a new workflow usecase
func NewWorkflowUsecase(repo WorkflowRepo, logger log.Logger) *WorkflowUsecase {
	return &WorkflowUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// ⚡ BUSINESS ENTITIES
// ============================================================================

// WorkflowRule represents a workflow automation rule
type WorkflowRule struct {
	ID                uint                   `json:"id"`
	RuleName          string                 `json:"rule_name"`
	Description       string                 `json:"description"`
	TriggerType       string                 `json:"trigger_type"`
	TriggerConditions []WorkflowCondition    `json:"trigger_conditions"`
	Actions           []WorkflowAction       `json:"actions"`
	Priority          int                    `json:"priority"`
	IsActive          bool                   `json:"is_active"`
	ExecutionCount    int                    `json:"execution_count"`
	SuccessCount      int                    `json:"success_count"`
	LastExecuted      *time.Time             `json:"last_executed"`
	CreatedBy         string                 `json:"created_by"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
}

// WorkflowExecution represents a workflow execution instance
type WorkflowExecution struct {
	ID                uint                     `json:"id"`
	WorkflowRuleID    uint                     `json:"workflow_rule_id"`
	WorkflowRule      *WorkflowRule            `json:"workflow_rule,omitempty"`
	TriggerEntityID   uint                     `json:"trigger_entity_id"`
	TriggerEntityType string                   `json:"trigger_entity_type"`
	ExecutionStatus   string                   `json:"execution_status"`
	StartedAt         time.Time                `json:"started_at"`
	CompletedAt       *time.Time               `json:"completed_at"`
	ExecutionTime     *time.Duration           `json:"execution_time"`
	ActionsExecuted   []WorkflowActionResult   `json:"actions_executed"`
	Results           map[string]interface{}   `json:"results"`
	ErrorMessage      string                   `json:"error_message"`
	Metadata          map[string]interface{}   `json:"metadata"`
}

// WorkflowTemplate represents a reusable workflow template
type WorkflowTemplate struct {
	ID          uint                   `json:"id"`
	TemplateName string                `json:"template_name"`
	Category    string                 `json:"category"`
	Description string                 `json:"description"`
	Template    map[string]interface{} `json:"template"`
	IsPublic    bool                   `json:"is_public"`
	UsageCount  int                    `json:"usage_count"`
	CreatedBy   string                 `json:"created_by"`
	CreatedAt   time.Time              `json:"created_at"`
}

// WorkflowCondition represents a single condition in a workflow
type WorkflowCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	LogicOp  string      `json:"logic_op,omitempty"`
}

// WorkflowAction represents a single action in a workflow
type WorkflowAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target,omitempty"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
	Delay      *time.Duration         `json:"delay,omitempty"`
}

// WorkflowActionResult represents the result of a single action
type WorkflowActionResult struct {
	Action    WorkflowAction `json:"action"`
	Success   bool           `json:"success"`
	Result    interface{}    `json:"result,omitempty"`
	Error     string         `json:"error,omitempty"`
	Duration  time.Duration  `json:"duration"`
}

// WorkflowResult represents the result of a workflow execution
type WorkflowResult struct {
	Success         bool                   `json:"success"`
	ActionsExecuted []WorkflowActionResult `json:"actions_executed"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	ExecutionTime   time.Duration          `json:"execution_time"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// ============================================================================
// ⚡ BUSINESS LOGIC METHODS
// ============================================================================

// CreateWorkflowRule creates a new workflow rule
func (uc *WorkflowUsecase) CreateWorkflowRule(ctx context.Context, rule *WorkflowRule) error {
	uc.log.WithContext(ctx).Infof("⚡ Creating workflow rule: %s", rule.RuleName)
	
	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	
	err := uc.repo.CreateWorkflowRule(ctx, rule)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to create workflow rule: %v", err)
		return err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Workflow rule created successfully: %s", rule.RuleName)
	return nil
}

// GetWorkflowRules returns workflow rules with optional filtering
func (uc *WorkflowUsecase) GetWorkflowRules(ctx context.Context, triggerType string, isActive *bool) ([]*WorkflowRule, error) {
	uc.log.WithContext(ctx).Infof("⚡ Getting workflow rules (type: %s, active: %v)", triggerType, isActive)
	
	rules, err := uc.repo.GetWorkflowRules(ctx, triggerType, isActive)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get workflow rules: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Workflow rules retrieved: %d items", len(rules))
	return rules, nil
}

// ExecuteWorkflowsForTrigger executes all applicable workflows for a trigger
func (uc *WorkflowUsecase) ExecuteWorkflowsForTrigger(ctx context.Context, triggerType string, entity interface{}, entityID uint) ([]WorkflowResult, error) {
	uc.log.WithContext(ctx).Infof("⚡ Executing workflows for trigger: %s (entity ID: %d)", triggerType, entityID)
	
	// Get applicable workflow rules
	isActive := true
	rules, err := uc.repo.GetWorkflowRules(ctx, triggerType, &isActive)
	if err != nil {
		return nil, err
	}
	
	var results []WorkflowResult
	
	for _, rule := range rules {
		// Check if conditions are met
		conditionsMet := uc.evaluateConditions(rule.TriggerConditions, entity)
		if !conditionsMet {
			uc.log.WithContext(ctx).Debugf("⏭️ Conditions not met for rule: %s", rule.RuleName)
			continue
		}
		
		// Execute workflow
		result := uc.executeWorkflow(ctx, rule, entity, entityID)
		results = append(results, result)
		
		// Update rule execution statistics
		uc.repo.UpdateRuleStatistics(ctx, rule.ID, result.Success)
	}
	
	uc.log.WithContext(ctx).Infof("✅ Workflows executed successfully: %d results", len(results))
	return results, nil
}

// executeWorkflow executes a single workflow rule
func (uc *WorkflowUsecase) executeWorkflow(ctx context.Context, rule *WorkflowRule, entity interface{}, entityID uint) WorkflowResult {
	startTime := time.Now()
	
	uc.log.WithContext(ctx).Infof("⚡ Executing workflow: %s", rule.RuleName)
	
	// Create execution record
	execution := &WorkflowExecution{
		WorkflowRuleID:    rule.ID,
		TriggerEntityID:   entityID,
		TriggerEntityType: rule.TriggerType,
		ExecutionStatus:   "running",
		StartedAt:         startTime,
	}
	
	uc.repo.CreateWorkflowExecution(ctx, execution)
	
	// Execute actions
	var actionResults []WorkflowActionResult
	allSuccess := true
	
	for _, action := range rule.Actions {
		// Apply delay if specified
		if action.Delay != nil {
			time.Sleep(*action.Delay)
		}
		
		actionResult := uc.executeAction(ctx, action, entity, entityID)
		actionResults = append(actionResults, actionResult)
		
		if !actionResult.Success {
			allSuccess = false
			uc.log.WithContext(ctx).Errorf("❌ Action failed in workflow %s: %s", rule.RuleName, actionResult.Error)
		}
	}
	
	// Calculate execution time
	executionTime := time.Since(startTime)
	
	// Update execution record
	completedAt := time.Now()
	status := "completed"
	if !allSuccess {
		status = "failed"
	}
	
	execution.ExecutionStatus = status
	execution.CompletedAt = &completedAt
	execution.ExecutionTime = &executionTime
	execution.ActionsExecuted = actionResults
	execution.Results = map[string]interface{}{
		"success":           allSuccess,
		"actions_executed":  len(actionResults),
		"execution_time_ms": executionTime.Milliseconds(),
	}
	
	uc.repo.UpdateWorkflowExecution(ctx, execution)
	
	result := WorkflowResult{
		Success:         allSuccess,
		ActionsExecuted: actionResults,
		ExecutionTime:   executionTime,
		Metadata: map[string]interface{}{
			"rule_id":      rule.ID,
			"rule_name":    rule.RuleName,
			"execution_id": execution.ID,
		},
	}
	
	if !allSuccess {
		result.ErrorMessage = "One or more actions failed"
	}
	
	return result
}

// executeAction executes a single workflow action
func (uc *WorkflowUsecase) executeAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	startTime := time.Now()
	
	uc.log.WithContext(ctx).Infof("⚡ Executing action: %s", action.Type)
	
	result := WorkflowActionResult{
		Action:   action,
		Success:  false,
		Duration: 0,
	}
	
	// Simulate action execution based on type
	switch action.Type {
	case "email":
		result = uc.executeEmailAction(ctx, action, entity, entityID)
	case "notification":
		result = uc.executeNotificationAction(ctx, action, entity, entityID)
	case "assignment":
		result = uc.executeAssignmentAction(ctx, action, entity, entityID)
	case "escalation":
		result = uc.executeEscalationAction(ctx, action, entity, entityID)
	case "create_job":
		result = uc.executeCreateJobAction(ctx, action, entity, entityID)
	case "update_status":
		result = uc.executeUpdateStatusAction(ctx, action, entity, entityID)
	case "webhook":
		result = uc.executeWebhookAction(ctx, action, entity, entityID)
	case "ai_analysis":
		result = uc.executeAIAnalysisAction(ctx, action, entity, entityID)
	default:
		result.Error = "Unknown action type: " + action.Type
		uc.log.WithContext(ctx).Errorf("❌ Unknown action type: %s", action.Type)
	}
	
	result.Duration = time.Since(startTime)
	
	if result.Success {
		uc.log.WithContext(ctx).Infof("✅ Action executed successfully: %s (%.2fms)", action.Type, float64(result.Duration.Nanoseconds())/1e6)
	} else {
		uc.log.WithContext(ctx).Errorf("❌ Action failed: %s - %s", action.Type, result.Error)
	}
	
	return result
}

// Action execution methods (simplified implementations)
func (uc *WorkflowUsecase) executeEmailAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate email sending
	uc.log.WithContext(ctx).Info("📧 Sending email...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"sent_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeNotificationAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate notification sending
	uc.log.WithContext(ctx).Info("🔔 Sending notification...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"sent_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeAssignmentAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate assignment
	uc.log.WithContext(ctx).Info("👤 Assigning task...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"assigned_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeEscalationAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate escalation
	uc.log.WithContext(ctx).Info("🚨 Escalating issue...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"escalated_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeCreateJobAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate job creation
	uc.log.WithContext(ctx).Info("🔧 Creating job...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"job_id": time.Now().Unix(), "created_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeUpdateStatusAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate status update
	uc.log.WithContext(ctx).Info("📝 Updating status...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"updated_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeWebhookAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate webhook call
	uc.log.WithContext(ctx).Info("🌐 Calling webhook...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"status_code": 200, "called_at": time.Now()},
	}
}

func (uc *WorkflowUsecase) executeAIAnalysisAction(ctx context.Context, action WorkflowAction, entity interface{}, entityID uint) WorkflowActionResult {
	// Simulate AI analysis
	uc.log.WithContext(ctx).Info("🧠 Running AI analysis...")
	return WorkflowActionResult{
		Action:  action,
		Success: true,
		Result:  map[string]interface{}{"confidence": 0.95, "analyzed_at": time.Now()},
	}
}

// evaluateConditions evaluates workflow conditions against an entity
func (uc *WorkflowUsecase) evaluateConditions(conditions []WorkflowCondition, entity interface{}) bool {
	if len(conditions) == 0 {
		return true // No conditions means always true
	}
	
	// Simplified condition evaluation
	// In a real implementation, this would parse the entity and evaluate conditions
	return true
}

// GetWorkflowTemplates returns available workflow templates
func (uc *WorkflowUsecase) GetWorkflowTemplates(ctx context.Context, category string) ([]*WorkflowTemplate, error) {
	uc.log.WithContext(ctx).Infof("⚡ Getting workflow templates (category: %s)", category)
	
	templates, err := uc.repo.GetWorkflowTemplates(ctx, category)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to get workflow templates: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Workflow templates retrieved: %d items", len(templates))
	return templates, nil
}

// CreateWorkflowFromTemplate creates a workflow rule from a template
func (uc *WorkflowUsecase) CreateWorkflowFromTemplate(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*WorkflowRule, error) {
	uc.log.WithContext(ctx).Infof("⚡ Creating workflow from template ID: %d", templateID)
	
	rule, err := uc.repo.CreateWorkflowFromTemplate(ctx, templateID, ruleName, customizations)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("Failed to create workflow from template: %v", err)
		return nil, err
	}
	
	uc.log.WithContext(ctx).Infof("✅ Workflow created from template successfully: %s", rule.RuleName)
	return rule, nil
}