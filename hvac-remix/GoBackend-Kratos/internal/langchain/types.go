package langchain

import (
	"time"
)

// 🎯 LangChain Types - Data structures for AI processing results

// HVACAnalysisResult represents the result of HVAC issue analysis
type HVACAnalysisResult struct {
	IssueID          string                 `json:"issue_id"`
	PrimaryProblem   string                 `json:"primary_problem"`
	Category         HVACIssueCategory      `json:"category"`
	UrgencyLevel     UrgencyLevel           `json:"urgency_level"`
	RootCauses       []string               `json:"root_causes"`
	ImmediateActions []ImmediateAction      `json:"immediate_actions"`
	Assessment       ProfessionalAssessment `json:"professional_assessment"`
	Prevention       PreventiveMeasures     `json:"prevention"`
	Confidence       float64                `json:"confidence"`
	ProcessedAt      time.Time              `json:"processed_at"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// HVACIssueCategory defines types of HVAC issues
type HVACIssueCategory string

const (
	CategoryHeating      HVACIssueCategory = "heating"
	CategoryCooling      HVACIssueCategory = "cooling"
	CategoryVentilation  HVACIssueCategory = "ventilation"
	CategoryMaintenance  HVACIssueCategory = "maintenance"
	CategoryInstallation HVACIssueCategory = "installation"
	CategoryEmergency    HVACIssueCategory = "emergency"
	CategoryElectrical   HVACIssueCategory = "electrical"
	CategoryPlumbing     HVACIssueCategory = "plumbing"
	CategoryOther        HVACIssueCategory = "other"
)

// UrgencyLevel defines the urgency of an issue
type UrgencyLevel string

const (
	UrgencyLow       UrgencyLevel = "low"
	UrgencyMedium    UrgencyLevel = "medium"
	UrgencyHigh      UrgencyLevel = "high"
	UrgencyEmergency UrgencyLevel = "emergency"
)

// ImmediateAction represents actions customer can take immediately
type ImmediateAction struct {
	Action          string   `json:"action"`
	Description     string   `json:"description"`
	SafetyLevel     string   `json:"safety_level"`
	RequiredTools   []string `json:"required_tools,omitempty"`
	EstimatedTime   string   `json:"estimated_time,omitempty"`
	WarningNotes    []string `json:"warning_notes,omitempty"`
	StopConditions  []string `json:"stop_conditions,omitempty"`
}

// ProfessionalAssessment represents professional repair assessment
type ProfessionalAssessment struct {
	RepairComplexity string            `json:"repair_complexity"`
	EstimatedCost    CostRange         `json:"estimated_cost"`
	RequiredParts    []RequiredPart    `json:"required_parts"`
	RequiredTools    []string          `json:"required_tools"`
	EstimatedTime    string            `json:"estimated_time"`
	SkillLevel       string            `json:"skill_level"`
	Recommendations  []string          `json:"recommendations"`
}

// CostRange represents estimated cost range
type CostRange struct {
	MinCost  float64 `json:"min_cost"`
	MaxCost  float64 `json:"max_cost"`
	Currency string  `json:"currency"`
	Notes    string  `json:"notes,omitempty"`
}

// RequiredPart represents parts needed for repair
type RequiredPart struct {
	PartName        string  `json:"part_name"`
	PartNumber      string  `json:"part_number,omitempty"`
	Quantity        int     `json:"quantity"`
	EstimatedCost   float64 `json:"estimated_cost,omitempty"`
	Availability    string  `json:"availability,omitempty"`
	Alternatives    []string `json:"alternatives,omitempty"`
}

// PreventiveMeasures represents prevention recommendations
type PreventiveMeasures struct {
	MaintenanceSchedule []MaintenanceTask `json:"maintenance_schedule"`
	WarningSignsToWatch []string          `json:"warning_signs"`
	PreventionTips      []string          `json:"prevention_tips"`
	RecommendedUpgrades []string          `json:"recommended_upgrades,omitempty"`
}

// MaintenanceTask represents a maintenance task
type MaintenanceTask struct {
	TaskName      string `json:"task_name"`
	Frequency     string `json:"frequency"`
	Description   string `json:"description"`
	Season        string `json:"season,omitempty"`
	Professional  bool   `json:"professional_required"`
	EstimatedCost string `json:"estimated_cost,omitempty"`
}

// EmailAnalysisResult represents the result of email analysis
type EmailAnalysisResult struct {
	EmailID          string                 `json:"email_id"`
	Classification   EmailClassification    `json:"classification"`
	ContentAnalysis  EmailContentAnalysis   `json:"content_analysis"`
	RoutingInfo      EmailRoutingInfo       `json:"routing_info"`
	ExtractedInfo    ExtractedEmailInfo     `json:"extracted_info"`
	ResponseSuggestions ResponseSuggestions `json:"response_suggestions"`
	ProcessedAt      time.Time              `json:"processed_at"`
	Confidence       float64                `json:"confidence"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// EmailClassification represents email classification details
type EmailClassification struct {
	Category  EmailCategory `json:"category"`
	Priority  Priority      `json:"priority"`
	Sentiment Sentiment     `json:"sentiment"`
	Language  string        `json:"language"`
	IsSpam    bool          `json:"is_spam"`
	IsUrgent  bool          `json:"is_urgent"`
}

// EmailCategory defines types of emails
type EmailCategory string

const (
	CategoryServiceRequest EmailCategory = "service_request"
	CategoryComplaint      EmailCategory = "complaint"
	CategoryInquiry        EmailCategory = "inquiry"
	CategoryAppointment    EmailCategory = "appointment"
	CategoryBilling        EmailCategory = "billing"
	CategoryEmergencyEmail EmailCategory = "emergency"
	CategorySales          EmailCategory = "sales"
	CategoryMaintenanceReq EmailCategory = "maintenance"
	CategoryFeedback       EmailCategory = "feedback"
	CategoryOtherEmail     EmailCategory = "other"
)

// Priority defines email priority levels
type Priority string

const (
	PriorityLow    Priority = "low"
	PriorityMedium Priority = "medium"
	PriorityHigh   Priority = "high"
	PriorityUrgent Priority = "urgent"
)

// Sentiment defines email sentiment
type Sentiment string

const (
	SentimentPositive Sentiment = "positive"
	SentimentNeutral  Sentiment = "neutral"
	SentimentNegative Sentiment = "negative"
	SentimentAngry    Sentiment = "angry"
)

// EmailContentAnalysis represents content analysis details
type EmailContentAnalysis struct {
	MainTopic       string   `json:"main_topic"`
	KeyIssues       []string `json:"key_issues"`
	CustomerIntent  string   `json:"customer_intent"`
	ActionRequired  string   `json:"action_required"`
	MentionedEquipment []string `json:"mentioned_equipment,omitempty"`
	TechnicalTerms  []string `json:"technical_terms,omitempty"`
}

// EmailRoutingInfo represents routing recommendations
type EmailRoutingInfo struct {
	Department         Department `json:"department"`
	SuggestedAssignee  string     `json:"suggested_assignee"`
	ResponseTimeline   string     `json:"response_timeline"`
	EscalationNeeded   bool       `json:"escalation_needed"`
	RequiredSkills     []string   `json:"required_skills,omitempty"`
	SimilarCases       []string   `json:"similar_cases,omitempty"`
}

// Department defines company departments
type Department string

const (
	DepartmentTechnical       Department = "technical"
	DepartmentSales           Department = "sales"
	DepartmentBilling         Department = "billing"
	DepartmentCustomerService Department = "customer_service"
	DepartmentEmergencyTeam   Department = "emergency"
	DepartmentManagement      Department = "management"
)

// ExtractedEmailInfo represents extracted structured information
type ExtractedEmailInfo struct {
	CustomerContact    ContactInfo           `json:"customer_contact"`
	ServiceAddress     Address               `json:"service_address,omitempty"`
	EquipmentDetails   []EquipmentInfo       `json:"equipment_details,omitempty"`
	PreferredContact   ContactMethod         `json:"preferred_contact"`
	AppointmentPrefs   AppointmentPreference `json:"appointment_preferences,omitempty"`
	BudgetMentioned    string                `json:"budget_mentioned,omitempty"`
	TimelineMentioned  string                `json:"timeline_mentioned,omitempty"`
}

// ContactInfo represents customer contact information
type ContactInfo struct {
	Name         string `json:"name,omitempty"`
	Email        string `json:"email,omitempty"`
	Phone        string `json:"phone,omitempty"`
	AlternatePhone string `json:"alternate_phone,omitempty"`
	Company      string `json:"company,omitempty"`
}

// Address represents service address
type Address struct {
	Street   string `json:"street,omitempty"`
	City     string `json:"city,omitempty"`
	State    string `json:"state,omitempty"`
	ZipCode  string `json:"zip_code,omitempty"`
	Country  string `json:"country,omitempty"`
	Notes    string `json:"notes,omitempty"`
}

// EquipmentInfo represents HVAC equipment details
type EquipmentInfo struct {
	Type         string `json:"type"`
	Brand        string `json:"brand,omitempty"`
	Model        string `json:"model,omitempty"`
	Age          string `json:"age,omitempty"`
	SerialNumber string `json:"serial_number,omitempty"`
	Location     string `json:"location,omitempty"`
	Condition    string `json:"condition,omitempty"`
}

// ContactMethod defines preferred contact methods
type ContactMethod string

const (
	ContactEmail ContactMethod = "email"
	ContactPhone ContactMethod = "phone"
	ContactText  ContactMethod = "text"
	ContactAny   ContactMethod = "any"
)

// AppointmentPreference represents appointment preferences
type AppointmentPreference struct {
	PreferredDays  []string `json:"preferred_days,omitempty"`
	PreferredTimes []string `json:"preferred_times,omitempty"`
	Urgency        string   `json:"urgency,omitempty"`
	Flexibility    string   `json:"flexibility,omitempty"`
	SpecialNotes   string   `json:"special_notes,omitempty"`
}

// ResponseSuggestions represents suggested response approach
type ResponseSuggestions struct {
	RecommendedTone    string   `json:"recommended_tone"`
	KeyPointsToAddress []string `json:"key_points_to_address"`
	InfoNeeded         []string `json:"additional_info_needed,omitempty"`
	FollowUpActions    []string `json:"follow_up_actions"`
	TemplateToUse      string   `json:"template_to_use,omitempty"`
	EstimatedResponseTime string `json:"estimated_response_time"`
}

// AnalysisResult represents general analysis results
type AnalysisResult struct {
	AnalysisID   string                 `json:"analysis_id"`
	AnalysisType string                 `json:"analysis_type"`
	Content      string                 `json:"content"`
	Results      map[string]interface{} `json:"results"`
	Insights     []string               `json:"insights"`
	Recommendations []string            `json:"recommendations"`
	Confidence   float64                `json:"confidence"`
	ProcessedAt  time.Time              `json:"processed_at"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// SimilarityResult represents similarity search results
type SimilarityResult struct {
	Index      int                    `json:"index"`
	Document   string                 `json:"document"`
	Similarity float64                `json:"similarity"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// ProcessingStatus represents the status of async processing
type ProcessingStatus struct {
	TaskID      string    `json:"task_id"`
	Status      string    `json:"status"`
	Progress    float64   `json:"progress"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Error       string    `json:"error,omitempty"`
	Result      interface{} `json:"result,omitempty"`
}
