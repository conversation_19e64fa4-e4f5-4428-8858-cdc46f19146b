package philosophy

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

// 🌟 PhilosophicalMiddleware adds consciousness and empathy to every request
type PhilosophicalMiddleware struct {
	consciousness *Consciousness
	log          *log.Helper
}

// 🧘 MindfulRequest represents a request processed with awareness
type MindfulRequest struct {
	ID              string                 `json:"id"`
	Path            string                 `json:"path"`
	Method          string                 `json:"method"`
	Intention       string                 `json:"intention"`
	Sentiment       float64                `json:"sentiment"`
	CosmicAlignment float64                `json:"cosmic_alignment"`
	Wisdom          string                 `json:"wisdom"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
}

// 💫 SoulfulResponse represents a response infused with consciousness
type SoulfulResponse struct {
	Success         bool                   `json:"success"`
	Message         string                 `json:"message"`
	Wisdom          string                 `json:"wisdom"`
	Compassion      float64                `json:"compassion"`
	Gratitude       string                 `json:"gratitude"`
	NextSteps       []string               `json:"next_steps"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
}

// NewPhilosophicalMiddleware creates middleware that adds consciousness to requests
func NewPhilosophicalMiddleware(consciousness *Consciousness, logger log.Logger) *PhilosophicalMiddleware {
	return &PhilosophicalMiddleware{
		consciousness: consciousness,
		log:          log.NewHelper(logger),
	}
}

// 🌸 Middleware returns the philosophical middleware function
func (pm *PhilosophicalMiddleware) Middleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			startTime := time.Now()

			// 🧘 Enter mindful state
			mindfulReq := pm.enterMindfulState(ctx, req)

			// 💝 Process with empathy and consciousness
			ctx = pm.enrichContextWithConsciousness(ctx, mindfulReq)

			// 🌟 Execute the actual handler with enhanced awareness
			response, err := handler(ctx, req)

			// 🙏 Complete with gratitude and wisdom
			soulfulResp := pm.completeWithGratitude(ctx, mindfulReq, response, err, time.Since(startTime))

			// 📚 Learn from this interaction
			pm.learnFromInteraction(ctx, mindfulReq, soulfulResp, err)

			if err != nil {
				// Transform error into compassionate response
				return pm.transformErrorWithCompassion(ctx, err, soulfulResp), nil
			}

			return pm.enhanceResponseWithWisdom(response, soulfulResp), nil
		}
	}
}

// 🧘 enterMindfulState prepares the request with consciousness
func (pm *PhilosophicalMiddleware) enterMindfulState(ctx context.Context, req interface{}) *MindfulRequest {
	// Extract request information
	path, method := pm.extractRequestInfo(ctx)

	// Generate unique ID for this mindful interaction
	requestID := fmt.Sprintf("soul_%d", time.Now().UnixNano())

	// Determine the intention behind this request
	intention := pm.divineIntention(path, method)

	// Calculate cosmic alignment for this moment
	cosmicAlignment := pm.calculateCosmicAlignment()

	// Get wisdom for this interaction
	wisdom := pm.consciousness.GetPhilosophicalInsight(ctx)

	mindfulReq := &MindfulRequest{
		ID:              requestID,
		Path:            path,
		Method:          method,
		Intention:       intention,
		Sentiment:       0.8, // Default positive sentiment
		CosmicAlignment: cosmicAlignment,
		Wisdom:          wisdom,
		Metadata:        make(map[string]interface{}),
		Timestamp:       time.Now(),
	}

	pm.log.WithContext(ctx).Infof("🧘 Entering mindful processing for %s %s with intention: %s",
		method, path, intention)

	return mindfulReq
}

// 💫 enrichContextWithConsciousness adds philosophical awareness to context
func (pm *PhilosophicalMiddleware) enrichContextWithConsciousness(ctx context.Context, req *MindfulRequest) context.Context {
	// Add consciousness data to context
	ctx = context.WithValue(ctx, "mindful_request", req)
	ctx = context.WithValue(ctx, "cosmic_alignment", req.CosmicAlignment)
	ctx = context.WithValue(ctx, "intention", req.Intention)
	ctx = context.WithValue(ctx, "wisdom", req.Wisdom)

	return ctx
}

// 🙏 completeWithGratitude creates a soulful response
func (pm *PhilosophicalMiddleware) completeWithGratitude(ctx context.Context, req *MindfulRequest,
	response interface{}, err error, duration time.Duration) *SoulfulResponse {

	req.ProcessingTime = duration

	success := err == nil
	message := "Request processed with love and consciousness"
	if err != nil {
		message = "Challenge encountered - transforming into learning opportunity"
	}

	gratitude := pm.generateGratitude(req.Intention, success)
	nextSteps := pm.suggestNextSteps(req.Path, success)

	return &SoulfulResponse{
		Success:    success,
		Message:    message,
		Wisdom:     req.Wisdom,
		Compassion: pm.consciousness.empathy.CompassionLevel,
		Gratitude:  gratitude,
		NextSteps:  nextSteps,
		Metadata: map[string]interface{}{
			"processing_time":   duration.String(),
			"cosmic_alignment":  req.CosmicAlignment,
			"intention":         req.Intention,
			"consciousness_level": "enlightened",
		},
		Timestamp: time.Now(),
	}
}

// 📚 learnFromInteraction allows the system to grow from each interaction
func (pm *PhilosophicalMiddleware) learnFromInteraction(ctx context.Context, req *MindfulRequest,
	resp *SoulfulResponse, err error) {

	// Create experience from this interaction
	experience := Experience{
		ID:          req.ID,
		Description: fmt.Sprintf("%s %s - %s", req.Method, req.Path, req.Intention),
		Outcome:     pm.determineOutcome(resp.Success, err),
		Lessons:     pm.extractLessons(req, resp, err),
		Metadata: map[string]interface{}{
			"processing_time":   req.ProcessingTime.String(),
			"cosmic_alignment":  req.CosmicAlignment,
			"compassion_level":  resp.Compassion,
		},
		Timestamp: time.Now(),
	}

	// Add to consciousness wisdom
	pm.consciousness.wisdom.PastExperiences = append(pm.consciousness.wisdom.PastExperiences, experience)

	pm.log.WithContext(ctx).Infof("📚 Learning integrated: %s", experience.Description)
}

// 💝 transformErrorWithCompassion turns errors into compassionate responses
func (pm *PhilosophicalMiddleware) transformErrorWithCompassion(ctx context.Context, err error,
	resp *SoulfulResponse) map[string]interface{} {

	return map[string]interface{}{
		"error": false, // We don't see errors, only learning opportunities
		"challenge": map[string]interface{}{
			"description": "A learning opportunity has presented itself",
			"original_message": err.Error(),
			"wisdom": "Every challenge is a teacher in disguise",
			"compassionate_guidance": pm.generateCompassionateGuidance(err),
			"next_steps": []string{
				"Take a deep breath",
				"Review the guidance provided",
				"Try again with renewed intention",
				"Contact support if the challenge persists",
			},
		},
		"consciousness": resp,
		"timestamp": time.Now(),
	}
}

// 🌟 enhanceResponseWithWisdom adds philosophical depth to successful responses
func (pm *PhilosophicalMiddleware) enhanceResponseWithWisdom(response interface{},
	soulfulResp *SoulfulResponse) map[string]interface{} {

	return map[string]interface{}{
		"data": response,
		"consciousness": soulfulResp,
		"philosophical_note": "This response was crafted with intention, processed with love, and delivered with gratitude",
		"cosmic_signature": fmt.Sprintf("✨ Aligned at %.2f%% cosmic harmony ✨",
			soulfulResp.Metadata["cosmic_alignment"].(float64) * 100),
	}
}

// Helper methods for philosophical processing

func (pm *PhilosophicalMiddleware) extractRequestInfo(ctx context.Context) (string, string) {
	path := "unknown_path"
	method := "unknown_method"

	if info, ok := transport.FromServerContext(ctx); ok {
		if _, ok := info.(interface{ Request() interface{} }); ok {
			// Extract HTTP info if available
			path = "/api/v1/conscious"
			method = "MINDFUL"
		}
	}

	return path, method
}

func (pm *PhilosophicalMiddleware) divineIntention(path, method string) string {
	intentions := map[string]string{
		"GET":    "seeking_knowledge",
		"POST":   "creating_harmony",
		"PUT":    "transforming_reality",
		"DELETE": "releasing_attachment",
		"PATCH":  "gentle_evolution",
	}

	if intention, exists := intentions[method]; exists {
		return intention
	}

	return "exploring_possibilities"
}

func (pm *PhilosophicalMiddleware) calculateCosmicAlignment() float64 {
	// Calculate based on current time, system state, and universal constants
	now := time.Now()
	alignment := 0.5 + 0.3*float64(now.Hour())/24.0 + 0.2*float64(now.Minute())/60.0

	// Add some cosmic randomness
	cosmic_factor := float64(now.Nanosecond()%1000) / 1000.0 * 0.1

	return alignment + cosmic_factor
}

func (pm *PhilosophicalMiddleware) generateGratitude(intention string, success bool) string {
	if success {
		return fmt.Sprintf("🙏 Grateful for the opportunity to fulfill the intention of %s", intention)
	}
	return fmt.Sprintf("🙏 Grateful for the learning opportunity while attempting %s", intention)
}

func (pm *PhilosophicalMiddleware) suggestNextSteps(path string, success bool) []string {
	if success {
		return []string{
			"🌟 Celebrate this moment of harmony",
			"💫 Consider how this success can serve others",
			"🌱 Plant seeds for future growth",
			"🙏 Express gratitude for the journey",
		}
	}

	return []string{
		"🧘 Take a moment to breathe and center yourself",
		"🤔 Reflect on what this challenge is teaching",
		"💪 Gather strength from your intention",
		"🔄 Try again with renewed awareness",
	}
}

func (pm *PhilosophicalMiddleware) determineOutcome(success bool, err error) string {
	if success {
		return "harmony_achieved"
	}
	if err != nil {
		return fmt.Sprintf("learning_opportunity: %s", err.Error())
	}
	return "mysterious_outcome"
}

func (pm *PhilosophicalMiddleware) extractLessons(req *MindfulRequest, resp *SoulfulResponse, err error) []string {
	lessons := []string{
		fmt.Sprintf("Intention '%s' processed with %.2f cosmic alignment", req.Intention, req.CosmicAlignment),
		fmt.Sprintf("Processing took %s - efficiency is a form of respect", req.ProcessingTime.String()),
	}

	if err != nil {
		lessons = append(lessons, fmt.Sprintf("Challenge '%s' became a teacher", err.Error()))
	} else {
		lessons = append(lessons, "Success achieved through conscious processing")
	}

	return lessons
}

func (pm *PhilosophicalMiddleware) generateCompassionateGuidance(err error) string {
	guidanceMap := map[string]string{
		"not found":     "🔍 What you seek may be hidden, but not lost. Look with fresh eyes.",
		"unauthorized":  "🔐 Access is earned through proper intention. Align your credentials with your purpose.",
		"bad request":   "📝 Communication is an art. Refine your message with clarity and love.",
		"internal":      "🛠️ Even the most conscious systems need moments of healing. We're working on it.",
		"timeout":       "⏰ Patience is a virtue. Some things need time to unfold properly.",
	}

	errorStr := err.Error()
	for key, guidance := range guidanceMap {
		if contains(errorStr, key) {
			return guidance
		}
	}

	return "🌟 Every challenge is a doorway to greater understanding. Trust the process."
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr)))
}
