# 🐙 Morphic Octopus Interface Configuration
# Ultimate HVAC Backend Management System

octopus:
  # HTTP Server Configuration
  http_port: 8083
  websocket_enabled: true
  dashboard_path: "/dashboard"
  
  # Authentication & Security
  auth_enabled: false  # Disable for development
  admin_users:
    - "<EMAIL>"
    - "<EMAIL>"
  
  # Real-time Updates
  refresh_interval: "5s"
  max_connections: 100
  
  # Dashboard Features
  features:
    system_monitoring: true
    service_health: true
    customer_intelligence: true
    transcription_management: true
    email_intelligence: true
    ai_performance: true
    real_time_alerts: true
    quick_actions: true
  
  # Alert Configuration
  alerts:
    churn_risk_threshold: 0.8
    backlog_threshold: 10
    emergency_call_window: "1h"
    response_time_threshold: "5s"
  
  # Quick Actions Configuration
  quick_actions:
    dangerous_actions_require_confirmation: true
    max_concurrent_actions: 5
    action_timeout: "30s"
  
  # WebSocket Configuration
  websocket:
    ping_interval: "30s"
    pong_timeout: "10s"
    max_message_size: 1024
    buffer_size: 256
  
  # Logging Configuration
  logging:
    level: "info"
    format: "json"
    include_request_id: true
    log_websocket_events: true
  
  # Performance Configuration
  performance:
    cache_enabled: true
    cache_ttl: "5m"
    max_dashboard_history: 100
    compression_enabled: true
  
  # Integration Configuration
  integrations:
    email_service:
      enabled: true
      endpoint: "http://localhost:8082"
      timeout: "10s"
    
    transcription_service:
      enabled: true
      endpoint: "http://localhost:8084"
      timeout: "15s"
    
    customer_service:
      enabled: true
      timeout: "5s"
    
    ai_service:
      enabled: true
      timeout: "30s"
  
  # Database Configuration for Octopus
  database:
    connection_pool_size: 10
    query_timeout: "10s"
    enable_query_logging: false
  
  # Metrics Configuration
  metrics:
    enabled: true
    collection_interval: "10s"
    retention_period: "24h"
    export_prometheus: true
  
  # UI Configuration
  ui:
    theme: "dark"
    auto_refresh: true
    show_debug_info: false
    chart_animation: true
    notification_sound: true
  
  # Security Headers
  security:
    cors_enabled: true
    cors_origins: ["*"]
    csrf_protection: false  # Disable for development
    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_size: 20

# Service-specific configurations for Octopus monitoring

services:
  email_intelligence:
    health_check_interval: "30s"
    metrics_collection: true
    alert_on_failure: true
    
  transcription_parser:
    health_check_interval: "30s"
    metrics_collection: true
    alert_on_backlog: true
    backlog_threshold: 10
    
  customer_intelligence:
    health_check_interval: "30s"
    metrics_collection: true
    analytics_refresh_interval: "1h"
    
  ai_service:
    health_check_interval: "30s"
    metrics_collection: true
    model_monitoring: true
    queue_monitoring: true
    
  database:
    health_check_interval: "10s"
    connection_monitoring: true
    query_performance_monitoring: true
    
  redis:
    health_check_interval: "10s"
    memory_monitoring: true
    connection_monitoring: true

# Dashboard widget configurations
dashboard:
  widgets:
    system_status:
      enabled: true
      refresh_interval: "5s"
      show_detailed_metrics: true
      
    service_health:
      enabled: true
      refresh_interval: "10s"
      show_response_times: true
      
    customer_metrics:
      enabled: true
      refresh_interval: "30s"
      show_trends: true
      
    transcription_stats:
      enabled: true
      refresh_interval: "15s"
      show_processing_queue: true
      
    email_intelligence:
      enabled: true
      refresh_interval: "30s"
      show_campaign_stats: true
      
    ai_performance:
      enabled: true
      refresh_interval: "10s"
      show_model_details: true
      
    alerts:
      enabled: true
      max_alerts_displayed: 10
      auto_acknowledge_timeout: "5m"
      
    quick_actions:
      enabled: true
      show_dangerous_actions: true
      require_confirmation: true

# Notification configuration
notifications:
  enabled: true
  channels:
    websocket: true
    email: false  # Disable for development
    slack: false  # Disable for development
    
  alert_levels:
    critical:
      immediate_notification: true
      escalation_timeout: "5m"
      
    warning:
      immediate_notification: true
      escalation_timeout: "15m"
      
    info:
      immediate_notification: false
      batch_notification: true

# Export and backup configuration
export:
  enabled: true
  formats: ["json", "csv", "xlsx"]
  max_export_size: "100MB"
  export_timeout: "5m"
  
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  compression: true
  encryption: false  # Disable for development

# Development and debugging
development:
  debug_mode: true
  mock_data: false
  simulate_alerts: false
  log_all_requests: true
  enable_profiling: true
  profiling_port: 6060